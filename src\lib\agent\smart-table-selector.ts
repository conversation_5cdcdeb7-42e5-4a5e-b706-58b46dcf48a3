/**
 * نظام ذكي لاختيار الجداول والأعمدة المناسبة حسب نوع الاستعلام
 */

export interface TableColumnMapping {
  table: string;
  columns: string[];
  purpose: string;
  category: string;
}

export interface QueryContext {
  intent: string;
  entities: string[];
  queryType: 'single_table' | 'join' | 'comparison';
  comparisonItems?: string[];
}

export class SmartTableSelector {
  
  // خريطة ذكية للجداول والأعمدة حسب الغرض
  private tableColumnMappings: TableColumnMapping[] = [
    // 🛍️ المنتجات والمبيعات
    {
      table: 'tbltemp_ItemsMain',
      columns: ['ItemID', 'ItemName', 'Quantity', 'Amount', 'Bonus'],
      purpose: 'أكثر منتج مبيعاً',
      category: 'products'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['ItemNumber', 'ItemType', 'UnitName', 'Barcode', 'UnitPrice', 'ItemDiscount'],
      purpose: 'تفاصيل المنتج',
      category: 'products'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['CategoryID', 'CategoryName', 'CategoryNumber', 'ItemTypeID'],
      purpose: 'حسب التصنيف',
      category: 'products'
    },
    
    // 👥 العملاء والموردين
    {
      table: 'tbltemp_ItemsMain',
      columns: ['ClientID', 'ClientName', 'Amount', 'Quantity', 'BranchID', 'BranchName'],
      purpose: 'أكثر عميل',
      category: 'customers'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['DistributorID', 'DistributorName', 'Amount', 'Quantity'],
      purpose: 'أكثر موزع',
      category: 'distributors'
    },
    
    // 📦 المستودعات والفروع
    {
      table: 'tbltemp_ItemsMain',
      columns: ['StoreID', 'StoreName', 'Quantity', 'Amount'],
      purpose: 'مستودع محدد',
      category: 'stores'
    },
    {
      table: 'tbltemp_ItemsMain',
      columns: ['BranchID', 'BranchName', 'Amount'],
      purpose: 'الفرع الأكثر مبيعاً',
      category: 'branches'
    },
    
    // 💵 المبالغ والأسعار
    {
      table: 'tbltemp_ItemsMain',
      columns: ['Amount', 'UnitPrice', 'Bonus'],
      purpose: 'إجمالي المبيعات',
      category: 'financial'
    },
    {
      table: 'tbltemp_Inv_MainInvoice',
      columns: ['TotalAmount', 'UnitPrice', 'Quantity'],
      purpose: 'إجمالي المبيعات',
      category: 'financial'
    },
    
    // 🕒 التواريخ
    {
      table: 'tbltemp_ItemsMain',
      columns: ['TheDate', 'ExpiryDate'],
      purpose: 'مبيعات حسب تاريخ',
      category: 'temporal'
    },
    {
      table: 'tbltemp_Inv_MainInvoice',
      columns: ['TheDate'],
      purpose: 'مبيعات حسب تاريخ',
      category: 'temporal'
    }
  ];

  /**
   * اختيار الجدول والأعمدة المناسبة حسب السياق
   */
  selectTableAndColumns(context: QueryContext): {
    primaryTable: string;
    secondaryTable?: string;
    columns: string[];
    joinCondition?: string;
    queryTemplate: string;
  } {
    const intent = context.intent.toLowerCase();
    
    // تحديد نوع الاستعلام
    if (intent.includes('منتج') || intent.includes('product') || intent.includes('item')) {
      return this.handleProductQuery(context);
    }
    
    if (intent.includes('عميل') || intent.includes('customer') || intent.includes('client')) {
      return this.handleCustomerQuery(context);
    }
    
    if (intent.includes('فرع') || intent.includes('branch')) {
      return this.handleBranchQuery(context);
    }
    
    if (intent.includes('مبيعات') || intent.includes('sales') || intent.includes('إجمالي')) {
      return this.handleSalesQuery(context);
    }
    
    // افتراضي: استخدام الجدول الرئيسي
    return this.getDefaultQuery();
  }

  /**
   * معالجة استعلامات المنتجات
   */
  private handleProductQuery(context: QueryContext): any {
    const intent = context.intent.toLowerCase();
    
    if (intent.includes('أكثر') || intent.includes('اكثر') || intent.includes('top')) {
      // أكثر المنتجات مبيعاً
      return {
        primaryTable: 'tbltemp_ItemsMain',
        columns: ['ItemID', 'ItemName', 'SUM(Quantity) as TotalQuantity', 'SUM(Amount) as TotalAmount'],
        queryTemplate: `
          SELECT TOP 10 ItemID, ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalAmount
          FROM tbltemp_ItemsMain 
          WHERE DocumentID LIKE '%مبيعات%'
          GROUP BY ItemID, ItemName 
          ORDER BY TotalQuantity DESC
        `
      };
    }
    
    if (context.queryType === 'comparison' && context.comparisonItems) {
      // مقارنة بين منتجات
      return this.handleProductComparison(context.comparisonItems);
    }
    
    return this.getDefaultQuery();
  }

  /**
   * معالجة استعلامات العملاء
   */
  private handleCustomerQuery(context: QueryContext): any {
    const intent = context.intent.toLowerCase();
    
    if (intent.includes('أكثر') || intent.includes('اكثر') || intent.includes('أفضل')) {
      // أكثر العملاء شراءً
      return {
        primaryTable: 'tbltemp_ItemsMain',
        columns: ['ClientID', 'ClientName', 'SUM(Amount) as TotalSpent', 'COUNT(*) as TransactionCount'],
        queryTemplate: `
          SELECT TOP 10 ClientID, ClientName, SUM(Amount) as TotalSpent, COUNT(*) as TransactionCount
          FROM tbltemp_ItemsMain 
          WHERE DocumentID LIKE '%مبيعات%' AND ClientName IS NOT NULL
          GROUP BY ClientID, ClientName 
          ORDER BY TotalSpent DESC
        `
      };
    }
    
    if (context.queryType === 'comparison' && context.comparisonItems) {
      // مقارنة بين عملاء
      return this.handleCustomerComparison(context.comparisonItems);
    }
    
    return this.getDefaultQuery();
  }

  /**
   * معالجة استعلامات الفروع
   */
  private handleBranchQuery(context: QueryContext): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['BranchID', 'BranchName', 'SUM(Amount) as TotalSales', 'COUNT(*) as TransactionCount'],
      queryTemplate: `
        SELECT BranchID, BranchName, SUM(Amount) as TotalSales, COUNT(*) as TransactionCount
        FROM tbltemp_ItemsMain 
        WHERE DocumentID LIKE '%مبيعات%' AND BranchName IS NOT NULL
        GROUP BY BranchID, BranchName 
        ORDER BY TotalSales DESC
      `
    };
  }

  /**
   * معالجة استعلامات المبيعات العامة
   */
  private handleSalesQuery(context: QueryContext): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['SUM(Amount) as TotalSales', 'COUNT(*) as TransactionCount', 'AVG(Amount) as AvgTransaction'],
      queryTemplate: `
        SELECT SUM(Amount) as TotalSales, COUNT(*) as TransactionCount, AVG(Amount) as AvgTransaction
        FROM tbltemp_ItemsMain 
        WHERE DocumentID LIKE '%مبيعات%'
      `
    };
  }

  /**
   * مقارنة بين منتجات
   */
  private handleProductComparison(items: string[]): any {
    const itemsCondition = items.map(item => `ItemName LIKE '%${item}%'`).join(' OR ');
    
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['ItemName', 'SUM(Quantity) as TotalQuantity', 'SUM(Amount) as TotalAmount', 'AVG(UnitPrice) as AvgPrice'],
      queryTemplate: `
        SELECT ItemName, SUM(Quantity) as TotalQuantity, SUM(Amount) as TotalAmount, AVG(UnitPrice) as AvgPrice
        FROM tbltemp_ItemsMain 
        WHERE DocumentID LIKE '%مبيعات%' AND (${itemsCondition})
        GROUP BY ItemName 
        ORDER BY TotalAmount DESC
      `
    };
  }

  /**
   * مقارنة بين عملاء
   */
  private handleCustomerComparison(customers: string[]): any {
    const customersCondition = customers.map(customer => `ClientName LIKE '%${customer}%'`).join(' OR ');
    
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['ClientName', 'SUM(Amount) as TotalSpent', 'COUNT(*) as TransactionCount', 'AVG(Amount) as AvgTransaction'],
      queryTemplate: `
        SELECT ClientName, SUM(Amount) as TotalSpent, COUNT(*) as TransactionCount, AVG(Amount) as AvgTransaction
        FROM tbltemp_ItemsMain 
        WHERE DocumentID LIKE '%مبيعات%' AND (${customersCondition})
        GROUP BY ClientName 
        ORDER BY TotalSpent DESC
      `
    };
  }

  /**
   * استعلام افتراضي
   */
  private getDefaultQuery(): any {
    return {
      primaryTable: 'tbltemp_ItemsMain',
      columns: ['COUNT(*) as TotalRecords'],
      queryTemplate: `
        SELECT COUNT(*) as TotalRecords
        FROM tbltemp_ItemsMain 
        WHERE DocumentID LIKE '%مبيعات%'
      `
    };
  }

  /**
   * الحصول على الأعمدة المتاحة لجدول معين
   */
  getAvailableColumns(tableName: string, category?: string): string[] {
    return this.tableColumnMappings
      .filter(mapping => mapping.table === tableName && (!category || mapping.category === category))
      .flatMap(mapping => mapping.columns);
  }

  /**
   * التحقق من وجود عمود في جدول
   */
  isColumnInTable(columnName: string, tableName: string): boolean {
    return this.tableColumnMappings
      .filter(mapping => mapping.table === tableName)
      .some(mapping => mapping.columns.includes(columnName));
  }
}
