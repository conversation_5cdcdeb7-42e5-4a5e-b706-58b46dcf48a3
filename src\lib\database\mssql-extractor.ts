import sql from 'mssql';
import { 
  DatabaseConnection, 
  TableInfo, 
  ColumnInfo, 
  ForeignKeyInfo, 
  IndexInfo,
  DatabaseSchema,
  RelationshipInfo 
} from './types';

export class MSSQLSchemaExtractor {
  private pool: sql.ConnectionPool | null = null;

  // اختبار مباشر بالبيانات المقدمة
  async testDirectConnection(): Promise<boolean> {
    const testConfig = {
      user: 'myuser',
      password: 'Aa227520',
      server: 'localhost',
      database: 'SalesTempDB',
      options: {
        encrypt: false,
        trustServerCertificate: true,
      },
      port: 1433,
    };

    try {
      console.log('🔄 محاولة الاتصال المباشر بالبيانات المقدمة...');
      console.log('📋 إعدادات الاتصال:', {
        server: testConfig.server,
        database: testConfig.database,
        user: testConfig.user,
        port: testConfig.port
      });

      const pool = await new sql.ConnectionPool(testConfig).connect();
      console.log('✅ نجح الاتصال المباشر!');

      // اختبار استعلام بسيط
      const result = await pool.request().query('SELECT @@VERSION as version, DB_NAME() as currentDB');
      console.log('📊 معلومات الخادم:', result.recordset[0]);

      // فحص الجداول الموجودة
      const tablesResult = await pool.request().query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
      `);
      console.log('📋 الجداول الموجودة:', tablesResult.recordset.map(row => row.TABLE_NAME));

      await pool.close();
      return true;
    } catch (error: any) {
      console.error('❌ فشل الاتصال المباشر:', error);
      console.error('🔍 MSSQL Direct Connection Error Details:', {
        code: error.code,
        message: error.message,
        originalError: error.originalError,
        stack: error.stack,
        fullError: error,
        stringified: JSON.stringify(error, null, 2)
      });

      // طباعة جميع خصائص الخطأ
      console.error('🔍 All Error Properties:');
      for (const key in error) {
        console.error(`  ${key}:`, error[key]);
      }

      return false;
    }
  }

  async connect(config: DatabaseConnection): Promise<void> {
    try {
      const sqlConfig: sql.config = {
        server: config.host,
        database: config.database,
        options: {
          encrypt: false,
          trustServerCertificate: true,
          enableArithAbort: true,
          instanceName: undefined // سيتم تحديده تلقائياً من اسم الخادم
        },
        connectionTimeout: 30000, // 30 ثانية
        requestTimeout: 30000,
        pool: {
          max: 10,
          min: 0,
          idleTimeoutMillis: 30000
        }
      };

      // استخراج اسم الـ instance إذا كان موجوداً
      if (config.host.includes('\\')) {
        const [serverName, instanceName] = config.host.split('\\');
        sqlConfig.server = serverName;
        sqlConfig.options!.instanceName = instanceName;
        console.log(`محاولة الاتصال بـ: ${serverName}\\${instanceName}`);
      }

      // تحديد المنفذ إذا لم يكن هناك instance name
      if (!sqlConfig.options!.instanceName && config.port && config.port !== 1433) {
        sqlConfig.port = config.port;
      }

      // إضافة المصادقة حسب النوع
      if (config.useWindowsAuth) {
        // Windows Authentication
        sqlConfig.options!.trustedConnection = true;
        console.log('استخدام Windows Authentication');
      } else {
        // SQL Server Authentication
        sqlConfig.user = config.username;
        sqlConfig.password = config.password;
        console.log('استخدام SQL Server Authentication');
      }

      console.log('إعدادات الاتصال:', {
        server: sqlConfig.server,
        instanceName: sqlConfig.options!.instanceName,
        port: sqlConfig.port,
        database: sqlConfig.database,
        authType: config.useWindowsAuth ? 'Windows' : 'SQL Server'
      });

      this.pool = await new sql.ConnectionPool(sqlConfig).connect();
      console.log('تم الاتصال بقاعدة بيانات SQL Server بنجاح');
    } catch (error: any) {
      console.error('خطأ في الاتصال بقاعدة البيانات:', error);

      let errorMessage = 'فشل الاتصال بقاعدة البيانات';

      if (error.code === 'ETIMEOUT') {
        errorMessage = `❌ انتهت مهلة الاتصال بالخادم ${config.host}

🔧 الحلول المقترحة:
1. تأكد من أن SQL Server يعمل
2. فعّل TCP/IP في SQL Server Configuration Manager
3. تأكد من أن SQL Server Browser يعمل
4. تحقق من إعدادات الجدار الناري`;
      } else if (error.code === 'ESOCKET') {
        errorMessage = `❌ لا يمكن الاتصال بالخادم ${config.host}

🔧 الحلول المقترحة:
1. تحقق من اسم الخادم (${config.host})
2. تأكد من أن SQL Server يعمل
3. فعّل TCP/IP Protocol
4. أعد تشغيل SQL Server Service`;
      } else if (error.code === 'ELOGIN') {
        errorMessage = `❌ خطأ في المصادقة

🔧 الحلول المقترحة:
1. تحقق من اسم المستخدم وكلمة المرور
2. جرب Windows Authentication
3. تأكد من أن SQL Server Authentication مفعل`;
      } else if (error.message?.includes('Named Pipes')) {
        errorMessage = `❌ مشكلة في Named Pipes

🔧 الحل:
فعّل TCP/IP Protocol في SQL Server Configuration Manager`;
      } else {
        errorMessage = `❌ خطأ في الاتصال: ${error.message || error}

💡 نصيحة: تحقق من إعدادات SQL Server Network Configuration`;
      }

      // إظهار الخطأ الأصلي من SQL Server للتشخيص
      console.error('🔴 MSSQL Connection Error Details:', {
        code: error.code,
        message: error.message,
        originalError: error.originalError,
        stack: error.stack,
        fullError: error
      });

      // إرجاع الخطأ الأصلي مع التفاصيل
      throw new Error(`${errorMessage}\n\n🔍 Original Error: ${error.message}\n📋 Error Code: ${error.code}\n🔗 Full Details: ${JSON.stringify(error, null, 2)}`);
    }
  }

  async disconnect(): Promise<void> {
    if (this.pool) {
      await this.pool.close();
      this.pool = null;
    }
  }

  async extractSchema(databaseName: string): Promise<DatabaseSchema> {
    if (!this.pool) {
      throw new Error('لا يوجد اتصال بقاعدة البيانات');
    }

    try {
      const tables = await this.getTables();
      const tablesWithDetails = await Promise.all(
        tables.map(tableName => this.getTableDetails(tableName))
      );

      const relationships = await this.getRelationships();

      return {
        databaseName,
        databaseType: 'mssql',
        tables: tablesWithDetails,
        relationships,
        extractedAt: new Date().toISOString(),
        version: '1.0'
      };
    } catch (error) {
      console.error('خطأ في استخراج schema:', error);
      throw error;
    }
  }

  private async getTables(): Promise<string[]> {
    const result = await this.pool!.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    
    return result.recordset.map((row: any) => row.TABLE_NAME);
  }

  private async getTableDetails(tableName: string): Promise<TableInfo> {
    const columns = await this.getColumns(tableName);
    const foreignKeys = await this.getForeignKeys(tableName);
    const indexes = await this.getIndexes(tableName);
    const primaryKeys = await this.getPrimaryKeys(tableName);
    const rowCount = await this.getRowCount(tableName);

    return {
      name: tableName,
      columns,
      foreignKeys,
      indexes,
      primaryKeys,
      rowCount
    };
  }

  private async getColumns(tableName: string): Promise<ColumnInfo[]> {
    const result = await this.pool!.request()
      .input('tableName', sql.VarChar, tableName)
      .query(`
        SELECT 
          c.COLUMN_NAME,
          c.DATA_TYPE,
          c.IS_NULLABLE,
          c.COLUMN_DEFAULT,
          c.CHARACTER_MAXIMUM_LENGTH,
          c.NUMERIC_PRECISION,
          c.NUMERIC_SCALE,
          CASE WHEN pk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_PRIMARY_KEY,
          CASE WHEN fk.COLUMN_NAME IS NOT NULL THEN 1 ELSE 0 END as IS_FOREIGN_KEY
        FROM INFORMATION_SCHEMA.COLUMNS c
        LEFT JOIN (
          SELECT ku.COLUMN_NAME
          FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
          JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
          WHERE tc.TABLE_NAME = @tableName AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ) pk ON c.COLUMN_NAME = pk.COLUMN_NAME
        LEFT JOIN (
          SELECT ku.COLUMN_NAME
          FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
          JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
          WHERE tc.TABLE_NAME = @tableName AND tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
        ) fk ON c.COLUMN_NAME = fk.COLUMN_NAME
        WHERE c.TABLE_NAME = @tableName
        ORDER BY c.ORDINAL_POSITION
      `);

    return result.recordset.map((row: any) => ({
      name: row.COLUMN_NAME,
      type: row.DATA_TYPE,
      nullable: row.IS_NULLABLE === 'YES',
      defaultValue: row.COLUMN_DEFAULT,
      isPrimaryKey: row.IS_PRIMARY_KEY === 1,
      isForeignKey: row.IS_FOREIGN_KEY === 1,
      maxLength: row.CHARACTER_MAXIMUM_LENGTH,
      precision: row.NUMERIC_PRECISION,
      scale: row.NUMERIC_SCALE
    }));
  }

  private async getForeignKeys(tableName: string): Promise<ForeignKeyInfo[]> {
    const result = await this.pool!.request()
      .input('tableName', sql.VarChar, tableName)
      .query(`
        SELECT 
          ku.COLUMN_NAME,
          ku2.TABLE_NAME as REFERENCED_TABLE_NAME,
          ku2.COLUMN_NAME as REFERENCED_COLUMN_NAME,
          rc.CONSTRAINT_NAME
        FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON rc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku2 ON rc.UNIQUE_CONSTRAINT_NAME = ku2.CONSTRAINT_NAME
        WHERE ku.TABLE_NAME = @tableName
      `);

    return result.recordset.map((row: any) => ({
      columnName: row.COLUMN_NAME,
      referencedTable: row.REFERENCED_TABLE_NAME,
      referencedColumn: row.REFERENCED_COLUMN_NAME,
      constraintName: row.CONSTRAINT_NAME
    }));
  }

  private async getIndexes(tableName: string): Promise<IndexInfo[]> {
    const result = await this.pool!.request()
      .input('tableName', sql.VarChar, tableName)
      .query(`
        SELECT 
          i.name as INDEX_NAME,
          c.name as COLUMN_NAME,
          i.is_unique,
          i.is_primary_key
        FROM sys.indexes i
        JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
        JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        JOIN sys.tables t ON i.object_id = t.object_id
        WHERE t.name = @tableName
        ORDER BY i.name, ic.key_ordinal
      `);

    const indexMap = new Map<string, IndexInfo>();
    
    result.recordset.forEach((row: any) => {
      const indexName = row.INDEX_NAME;
      if (!indexMap.has(indexName)) {
        indexMap.set(indexName, {
          name: indexName,
          columns: [],
          isUnique: row.is_unique,
          isPrimary: row.is_primary_key
        });
      }
      indexMap.get(indexName)!.columns.push(row.COLUMN_NAME);
    });

    return Array.from(indexMap.values());
  }

  private async getPrimaryKeys(tableName: string): Promise<string[]> {
    const result = await this.pool!.request()
      .input('tableName', sql.VarChar, tableName)
      .query(`
        SELECT ku.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
        WHERE tc.TABLE_NAME = @tableName AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ORDER BY ku.ORDINAL_POSITION
      `);

    return result.recordset.map((row: any) => row.COLUMN_NAME);
  }

  private async getRowCount(tableName: string): Promise<number> {
    try {
      const result = await this.pool!.request()
        .input('tableName', sql.VarChar, tableName)
        .query(`SELECT COUNT(*) as count FROM [${tableName}]`);
      return result.recordset[0].count;
    } catch {
      return 0;
    }
  }

  private async getRelationships(): Promise<RelationshipInfo[]> {
    const result = await this.pool!.request().query(`
      SELECT 
        ku.TABLE_NAME as fromTable,
        ku.COLUMN_NAME as fromColumn,
        ku2.TABLE_NAME as toTable,
        ku2.COLUMN_NAME as toColumn,
        rc.CONSTRAINT_NAME as constraintName
      FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
      JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON rc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
      JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku2 ON rc.UNIQUE_CONSTRAINT_NAME = ku2.CONSTRAINT_NAME
    `);

    return result.recordset.map((row: any) => ({
      fromTable: row.fromTable,
      fromColumn: row.fromColumn,
      toTable: row.toTable,
      toColumn: row.toColumn,
      relationshipType: 'one-to-many' as const,
      constraintName: row.constraintName
    }));
  }

  // تنفيذ استعلام SQL وإرجاع النتائج
  async executeQuery(query: string): Promise<any[]> {
    if (!this.pool) {
      throw new Error('لا يوجد اتصال نشط بقاعدة البيانات');
    }

    try {
      console.log('تنفيذ الاستعلام:', query);
      const result = await this.pool.request().query(query);
      return result.recordset || [];
    } catch (error) {
      console.error('خطأ في تنفيذ الاستعلام:', error);

      // معالجة خاصة لخطأ Ambiguous column name
      if (error instanceof Error && error.message.includes('Ambiguous column name')) {
        const columnName = error.message.match(/'([^']+)'/)?.[1];
        throw new Error(`خطأ في الاستعلام: العمود '${columnName}' موجود في أكثر من جدول. يرجى تحديد اسم الجدول (مثال: table1.${columnName})`);
      }

      throw new Error(`فشل في تنفيذ الاستعلام: ${error instanceof Error ? error.message : error}`);
    }
  }

  // الحصول على قائمة قواعد البيانات المتاحة
  async getDatabases(): Promise<string[]> {
    if (!this.pool) {
      throw new Error('لا يوجد اتصال نشط بقاعدة البيانات');
    }

    try {
      const result = await this.pool.request().query(`
        SELECT name
        FROM sys.databases
        WHERE database_id > 4 -- تجاهل قواعد البيانات النظام
        AND state = 0 -- قواعد البيانات المتاحة فقط
        ORDER BY name
      `);

      return result.recordset.map((row: any) => row.name);
    } catch (error) {
      console.error('خطأ في الحصول على قواعد البيانات:', error);
      throw new Error(`فشل في الحصول على قواعد البيانات: ${error instanceof Error ? error.message : error}`);
    }
  }

  // اتصال بدون تحديد قاعدة بيانات (للحصول على قائمة قواعد البيانات)
  async connectWithoutDatabase(config: Omit<DatabaseConnection, 'database'>): Promise<void> {
    try {
      const sqlConfig: sql.config = {
        server: config.host,
        options: {
          encrypt: false,
          trustServerCertificate: true,
          enableArithAbort: true,
          instanceName: undefined
        },
        connectionTimeout: 30000,
        requestTimeout: 30000,
        pool: {
          max: 10,
          min: 0,
          idleTimeoutMillis: 30000
        }
      };

      // استخراج اسم الـ instance إذا كان موجوداً
      if (config.host.includes('\\')) {
        const [serverName, instanceName] = config.host.split('\\');
        sqlConfig.server = serverName;
        sqlConfig.options!.instanceName = instanceName;
      }

      // تحديد المنفذ إذا لم يكن هناك instance name
      if (!sqlConfig.options!.instanceName && config.port && config.port !== 1433) {
        sqlConfig.port = config.port;
      }

      // إضافة المصادقة حسب النوع
      if (config.useWindowsAuth) {
        // Windows Authentication
        sqlConfig.options!.trustedConnection = true;
      } else {
        // SQL Server Authentication
        sqlConfig.user = config.username;
        sqlConfig.password = config.password;
      }

      this.pool = await new sql.ConnectionPool(sqlConfig).connect();
      console.log('تم الاتصال بخادم SQL Server بنجاح');
    } catch (error: any) {
      console.error('خطأ في الاتصال بالخادم:', error);

      let errorMessage = 'فشل الاتصال بالخادم';

      if (error.code === 'ETIMEOUT') {
        errorMessage = `❌ انتهت مهلة الاتصال بالخادم ${config.host}

🔧 الحلول المقترحة:
1. تأكد من أن SQL Server يعمل
2. فعّل TCP/IP في SQL Server Configuration Manager
3. تأكد من أن SQL Server Browser يعمل
4. تحقق من إعدادات الجدار الناري`;
      } else if (error.code === 'ESOCKET') {
        errorMessage = `❌ لا يمكن الاتصال بالخادم ${config.host}

🔧 الحلول المقترحة:
1. تحقق من اسم الخادم (${config.host})
2. تأكد من أن SQL Server يعمل
3. فعّل TCP/IP Protocol
4. أعد تشغيل SQL Server Service`;
      } else if (error.code === 'ELOGIN') {
        errorMessage = `❌ خطأ في المصادقة

🔧 الحلول المقترحة:
1. تحقق من اسم المستخدم وكلمة المرور
2. جرب Windows Authentication
3. تأكد من أن SQL Server Authentication مفعل`;
      } else {
        errorMessage = `❌ خطأ في الاتصال: ${error.message || error}

💡 نصيحة: تحقق من إعدادات SQL Server Network Configuration`;
      }

      // إظهار الخطأ الأصلي من SQL Server للتشخيص
      console.error('🔴 MSSQL connectWithoutDatabase Error Details:', {
        code: error.code,
        message: error.message,
        originalError: error.originalError,
        stack: error.stack,
        fullError: error
      });

      // إرجاع الخطأ الأصلي مع التفاصيل
      throw new Error(`${errorMessage}\n\n🔍 Original Error: ${error.message}\n📋 Error Code: ${error.code}\n🔗 Full Details: ${JSON.stringify(error, null, 2)}`);
    }
  }
}
