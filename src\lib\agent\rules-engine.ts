import rulesData from './rules.json';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedQuery?: string;
  appliedRules: string[];
}

export interface UserContext {
  userId: string;
  role: 'admin' | 'manager' | 'employee' | 'viewer';
  branchId?: string;
  permissions: string[];
}

export interface QueryContext {
  query: string;
  intent: string;
  estimatedRows: number;
  executionTimeMs?: number;
  tables: string[];
  columns: string[];
}

/**
 * محرك القواعد والقيود - يضمن الأمان والامتثال للقواعد التجارية
 */
export class RulesEngine {
  private securityRules: any;
  private accessControl: any;
  private dataPrivacy: any;
  private queryValidation: any;
  private businessRules: any;
  private performanceRules: any;
  private auditRules: any;
  private errorHandling: any;
  private compliance: any;

  constructor() {
    this.securityRules = rulesData.security_rules;
    this.accessControl = rulesData.access_control;
    this.dataPrivacy = rulesData.data_privacy;
    this.queryValidation = rulesData.query_validation;
    this.businessRules = rulesData.business_rules;
    this.performanceRules = rulesData.performance_rules;
    this.auditRules = rulesData.audit_rules;
    this.errorHandling = rulesData.error_handling;
    this.compliance = rulesData.compliance;
  }

  /**
   * التحقق الشامل من الاستعلام والسياق
   */
  validateQuery(
    query: string,
    userContext: UserContext,
    queryContext: QueryContext
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      appliedRules: []
    };

    // 1. فحص الأمان
    const securityCheck = this.validateSecurity(query);
    this.mergeValidationResult(result, securityCheck);

    // 2. فحص صلاحيات الوصول
    const accessCheck = this.validateAccess(userContext, queryContext);
    this.mergeValidationResult(result, accessCheck);

    // 3. فحص خصوصية البيانات
    const privacyCheck = this.validateDataPrivacy(query, userContext);
    this.mergeValidationResult(result, privacyCheck);

    // 4. فحص صحة الاستعلام
    const queryCheck = this.validateQueryStructure(query);
    this.mergeValidationResult(result, queryCheck);

    // 5. فحص القواعد التجارية
    const businessCheck = this.validateBusinessRules(queryContext);
    this.mergeValidationResult(result, businessCheck);

    // 6. فحص الأداء
    const performanceCheck = this.validatePerformance(queryContext);
    this.mergeValidationResult(result, performanceCheck);

    // تطبيق التنظيف والتعديل إذا لزم الأمر
    if (result.isValid) {
      result.sanitizedQuery = this.sanitizeQuery(query, userContext);
    }

    return result;
  }

  /**
   * فحص الأمان ضد SQL Injection
   */
  private validateSecurity(query: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      appliedRules: ['security_check']
    };

    const upperQuery = query.toUpperCase();

    // فحص أنماط SQL Injection
    for (const pattern of this.securityRules.sql_injection_patterns) {
      if (upperQuery.includes(pattern.toUpperCase())) {
        result.isValid = false;
        result.errors.push(`نمط خطير مكتشف: ${pattern}`);
      }
    }

    // فحص العمليات المحظورة
    for (const operation of this.securityRules.forbidden_operations) {
      const regex = new RegExp(`\\b${operation}\\b`, 'i');
      if (regex.test(query)) {
        result.isValid = false;
        result.errors.push(`عملية محظورة: ${operation}`);
      }
    }

    // فحص طول الاستعلام
    if (query.length > this.securityRules.max_query_length) {
      result.isValid = false;
      result.errors.push(`الاستعلام طويل جداً (${query.length} > ${this.securityRules.max_query_length})`);
    }

    return result;
  }

  /**
   * فحص صلاحيات الوصول
   */
  private validateAccess(userContext: UserContext, queryContext: QueryContext): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      appliedRules: ['access_control']
    };

    const userRole = this.accessControl.user_roles[userContext.role];
    if (!userRole) {
      result.isValid = false;
      result.errors.push(`دور مستخدم غير صالح: ${userContext.role}`);
      return result;
    }

    // فحص الجداول المسموحة
    if (userRole.accessible_tables[0] !== '*') {
      for (const table of queryContext.tables) {
        if (!userRole.accessible_tables.includes(table)) {
          result.isValid = false;
          result.errors.push(`ليس لديك صلاحية للوصول إلى الجدول: ${table}`);
        }
      }
    }

    // فحص الأعمدة المسموحة
    if (userRole.accessible_columns[0] !== '*') {
      for (const column of queryContext.columns) {
        if (!userRole.accessible_columns.includes(column)) {
          result.isValid = false;
          result.errors.push(`ليس لديك صلاحية للوصول إلى العمود: ${column}`);
        }
      }
    }

    // فحص البيانات الحساسة
    if (!userRole.can_access_sensitive_data) {
      for (const column of queryContext.columns) {
        if (this.accessControl.sensitive_columns.includes(column)) {
          result.isValid = false;
          result.errors.push(`ليس لديك صلاحية للوصول إلى البيانات الحساسة: ${column}`);
        }
      }
    }

    // فحص حد الصفوف
    if (queryContext.estimatedRows > userRole.max_rows_per_query) {
      result.warnings.push(`عدد الصفوف المتوقع (${queryContext.estimatedRows}) يتجاوز الحد المسموح (${userRole.max_rows_per_query})`);
    }

    return result;
  }

  /**
   * فحص خصوصية البيانات
   */
  private validateDataPrivacy(query: string, userContext: UserContext): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      appliedRules: ['data_privacy']
    };

    // فحص قواعد إخفاء الهوية
    const anonymizationRules = this.dataPrivacy.anonymization_rules;
    
    if (anonymizationRules.client_names.enabled && query.includes('ClientName')) {
      if (userContext.role === 'viewer' || userContext.role === 'employee') {
        result.warnings.push('سيتم إخفاء أسماء العملاء في النتائج');
      }
    }

    return result;
  }

  /**
   * فحص بنية الاستعلام
   */
  private validateQueryStructure(query: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      appliedRules: ['query_structure']
    };

    const upperQuery = query.toUpperCase();

    // فحص عدد الـ JOINs
    const joinCount = (upperQuery.match(/\bJOIN\b/g) || []).length;
    if (joinCount > this.queryValidation.max_joins) {
      result.isValid = false;
      result.errors.push(`عدد الـ JOINs (${joinCount}) يتجاوز الحد المسموح (${this.queryValidation.max_joins})`);
    }

    // فحص عدد الاستعلامات الفرعية
    const subqueryCount = (upperQuery.match(/\(\s*SELECT\b/g) || []).length;
    if (subqueryCount > this.queryValidation.max_subqueries) {
      result.isValid = false;
      result.errors.push(`عدد الاستعلامات الفرعية (${subqueryCount}) يتجاوز الحد المسموح (${this.queryValidation.max_subqueries})`);
    }

    // فحص الدوال المسموحة
    const functionRegex = /\b(\w+)\s*\(/g;
    let match;
    while ((match = functionRegex.exec(upperQuery)) !== null) {
      const functionName = match[1];
      if (!this.queryValidation.allowed_functions.includes(functionName) && 
          !this.securityRules.allowed_operations.includes(functionName)) {
        result.warnings.push(`دالة غير مألوفة: ${functionName}`);
      }
    }

    return result;
  }

  /**
   * فحص القواعد التجارية
   */
  private validateBusinessRules(queryContext: QueryContext): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      appliedRules: ['business_rules']
    };

    // فحص نطاق التاريخ
    const reportingConstraints = this.businessRules.reporting_constraints;
    if (reportingConstraints.require_date_filter && !queryContext.query.includes('TheDate')) {
      result.warnings.push('يُنصح بإضافة فلتر تاريخ للحصول على نتائج أفضل');
    }

    return result;
  }

  /**
   * فحص قواعد الأداء
   */
  private validatePerformance(queryContext: QueryContext): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      appliedRules: ['performance_rules']
    };

    const resultLimits = this.performanceRules.result_limits;
    
    if (queryContext.estimatedRows > resultLimits.max_limit) {
      result.isValid = false;
      result.errors.push(`عدد الصفوف المتوقع (${queryContext.estimatedRows}) يتجاوز الحد الأقصى (${resultLimits.max_limit})`);
    }

    if (queryContext.executionTimeMs && 
        queryContext.executionTimeMs > this.performanceRules.query_optimization.max_execution_time_ms) {
      result.warnings.push('الاستعلام قد يستغرق وقتاً طويلاً للتنفيذ');
    }

    return result;
  }

  /**
   * تنظيف الاستعلام وتطبيق القيود
   */
  private sanitizeQuery(query: string, userContext: UserContext): string {
    let sanitizedQuery = query;

    // إضافة LIMIT أو TOP حسب نوع قاعدة البيانات
    if (!sanitizedQuery.toUpperCase().includes('LIMIT') && !sanitizedQuery.toUpperCase().includes('TOP ')) {
      const userRole = this.accessControl.user_roles[userContext.role];
      const defaultLimit = Math.min(
        userRole.max_rows_per_query,
        this.performanceRules.result_limits.default_limit
      );

      // تحديد نوع قاعدة البيانات من الاستعلام أو السياق
      const isSQLServer = sanitizedQuery.includes('tbltemp_') || sanitizedQuery.includes('[') ||
                         sanitizedQuery.toUpperCase().includes('TOP ');

      if (isSQLServer) {
        // لا نضيف شيء لأن SQL Server يستخدم TOP في بداية SELECT
        // والاستعلام المُولد يجب أن يحتوي على TOP بالفعل
      } else {
        // MySQL/PostgreSQL
        sanitizedQuery += ` LIMIT ${defaultLimit}`;
      }
    }

    // إضافة فلتر الفرع إذا كان مطلوباً
    const userRole = this.accessControl.user_roles[userContext.role];
    if (userRole.branch_restrictions === 'user_branch_only' && userContext.branchId) {
      if (!sanitizedQuery.toUpperCase().includes('BRANCHNAME')) {
        const whereClause = sanitizedQuery.toUpperCase().includes('WHERE') ? 'AND' : 'WHERE';
        sanitizedQuery += ` ${whereClause} BranchName = '${userContext.branchId}'`;
      }
    }

    return sanitizedQuery;
  }

  /**
   * دمج نتائج التحقق
   */
  private mergeValidationResult(target: ValidationResult, source: ValidationResult): void {
    if (!source.isValid) {
      target.isValid = false;
    }
    target.errors.push(...source.errors);
    target.warnings.push(...source.warnings);
    target.appliedRules.push(...source.appliedRules);
  }

  /**
   * تسجيل الاستعلام للمراجعة
   */
  logQuery(
    query: string,
    userContext: UserContext,
    result: any,
    executionTimeMs: number
  ): void {
    if (this.auditRules.log_all_queries) {
      const logEntry = {
        timestamp: new Date().toISOString(),
        userId: userContext.userId,
        userRole: userContext.role,
        query: query,
        executionTimeMs: executionTimeMs,
        resultRowCount: result?.length || 0,
        success: !!result
      };

      // في التطبيق الحقيقي، سيتم حفظ هذا في قاعدة البيانات
      console.log('Query Log:', logEntry);
    }
  }

  /**
   * الحصول على رسالة خطأ مناسبة للمستخدم
   */
  getUserFriendlyError(errorType: string): string {
    return this.errorHandling.user_friendly_messages[errorType] || 'حدث خطأ غير متوقع';
  }

  /**
   * فحص ما إذا كان المستخدم لديه صلاحية معينة
   */
  hasPermission(userContext: UserContext, permission: string): boolean {
    return userContext.permissions.includes(permission) || userContext.role === 'admin';
  }
}
