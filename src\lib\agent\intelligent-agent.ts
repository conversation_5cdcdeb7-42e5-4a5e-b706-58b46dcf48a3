import { IntentClassifier, ClassificationResult } from './intent-classifier';
import { SynonymProcessor, ProcessedQuery } from './synonym-processor';
import { PromptTemplateManager, GeneratedPrompt, SQLQueryResult } from './prompt-template-manager';
import { RulesEngine, ValidationResult, UserContext, QueryContext } from './rules-engine';
import { SchemaGenerator, DatabaseSchema } from './schema-generator';
import { AgentProfileGenerator, AgentProfile } from './agent-profile-generator';
import { OpenRouterClient } from '../ai/openrouter-client';
import { SchemaManager } from '../database/schema-manager';
import { SmartTableSelector, QueryContext as SmartQueryContext } from './smart-table-selector';

export interface AgentResponse {
  success: boolean;
  query?: string;
  explanation?: string;
  confidence?: number;
  results?: any[];
  error?: string;
  warnings?: string[];
  metadata: {
    intent: string;
    processingTimeMs: number;
    rulesApplied: string[];
    templateUsed?: string;
    estimatedRows?: number;
  };
}

export interface AgentConfig {
  enableCaching: boolean;
  maxRetries: number;
  timeoutMs: number;
  debugMode: boolean;
}

/**
 * الوكيل الذكي الرئيسي - ينسق بين جميع الطبقات لمعالجة استعلامات المستخدم
 */
export class IntelligentAgent {
  private intentClassifier: IntentClassifier;
  private synonymProcessor: SynonymProcessor;
  private promptTemplateManager: PromptTemplateManager;
  private rulesEngine: RulesEngine;
  private schemaGenerator: SchemaGenerator;
  private agentProfileGenerator: AgentProfileGenerator;
  private openRouterClient: OpenRouterClient;
  private config: AgentConfig;
  private smartTableSelector: SmartTableSelector;
  private cache: Map<string, any>;
  private databaseSchema: DatabaseSchema | null = null;
  private agentProfiles: Map<string, AgentProfile> = new Map();

  constructor(config: Partial<AgentConfig> = {}) {
    this.intentClassifier = new IntentClassifier();
    this.synonymProcessor = new SynonymProcessor();
    this.promptTemplateManager = new PromptTemplateManager();
    this.rulesEngine = new RulesEngine();
    this.schemaGenerator = new SchemaGenerator();
    this.agentProfileGenerator = new AgentProfileGenerator();
    this.openRouterClient = OpenRouterClient.getInstance();
    this.smartTableSelector = new SmartTableSelector();

    this.config = {
      enableCaching: true,
      maxRetries: 3,
      timeoutMs: 30000,
      debugMode: false,
      ...config
    };
    
    this.cache = new Map();

    // تهيئة Schema وProfiles تلقائياً
    this.initializeSchemaAndProfiles();
  }

  /**
   * تهيئة Schema وAgent Profiles
   */
  private async initializeSchemaAndProfiles(): Promise<void> {
    try {
      if (this.config.debugMode) {
        console.log('🔍 تهيئة Schema وAgent Profiles...');
      }

      // محاولة تحميل Schema من database-schema.json
      this.databaseSchema = await this.schemaGenerator.loadSchemaFromFile();

      if (!this.databaseSchema || !this.databaseSchema.tables || this.databaseSchema.tables.length === 0) {
        if (this.config.debugMode) {
          console.log('📊 تحويل Schema من schema.json إلى database-schema.json...');
        }

        // محاولة تحميل من schema.json
        const schemaManager = SchemaManager.getInstance();
        let rawSchema = null;

        try {
          rawSchema = await schemaManager.loadSchema();
          if (this.config.debugMode) {
            console.log(`📊 تم تحميل Schema من schema.json: ${rawSchema?.tables?.length || 0} جدول`);
          }
        } catch (error) {
          if (this.config.debugMode) {
            console.log('⚠️ فشل في تحميل schema.json:', error);
          }
        }

        if (rawSchema && rawSchema.tables && rawSchema.tables.length > 0) {
          // تحويل Schema الخام إلى DatabaseSchema مع إضافة الأوصاف
          this.databaseSchema = await this.convertRawSchemaToEnhanced(rawSchema);

          // حفظ Schema المحسن
          await this.schemaGenerator.saveSchemaToFile(this.databaseSchema);

          if (this.config.debugMode) {
            console.log(`✅ تم تحويل Schema: ${this.databaseSchema.tables.length} جدول`);
          }
        } else {
          if (this.config.debugMode) {
            console.log('📊 توليد Schema جديد من قاعدة البيانات...');
          }
          // توليد Schema جديد من قاعدة البيانات
          this.databaseSchema = await this.schemaGenerator.generateDatabaseSchema();
          await this.schemaGenerator.saveSchemaToFile(this.databaseSchema);
        }
      }

      // تحميل Agent Profiles للجداول المهمة
      if (this.databaseSchema) {
        for (const table of this.databaseSchema.tables) {
          try {
            let profile = await this.agentProfileGenerator.loadProfileFromFile(`agent-profile-${table.name}.json`);

            if (!profile) {
              if (this.config.debugMode) {
                console.log(`🤖 توليد Agent Profile للجدول: ${table.name}`);
              }
              profile = await this.agentProfileGenerator.generateAgentProfile(table.name, this.databaseSchema);
              if (profile) {
                await this.agentProfileGenerator.saveProfileToFile(profile);
              }
            }

            if (profile) {
              this.agentProfiles.set(table.name, profile);
            }
          } catch (error) {
            if (this.config.debugMode) {
              console.warn(`⚠️ فشل في تحميل/توليد Profile للجدول ${table.name}:`, error);
            }
          }
        }
      }

      if (this.config.debugMode) {
        console.log(`✅ تم تهيئة ${this.agentProfiles.size} Agent Profile`);
      }

    } catch (error) {
      if (this.config.debugMode) {
        console.warn('⚠️ فشل في تهيئة Schema وProfiles:', error);
      }
    }
  }

  /**
   * معالجة استعلام المستخدم الرئيسية
   */
  async processQuery(
    userQuery: string,
    userContext: UserContext,
    tableInfo: string
  ): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      if (this.config.debugMode) {
        console.log('🤖 بدء معالجة الاستعلام:', userQuery);
      }

      // 1. تصنيف النية
      const classificationResult = await this.classifyIntent(userQuery);
      if (!classificationResult) {
        return this.createErrorResponse('لم أتمكن من فهم استعلامك. يرجى إعادة صياغته.', startTime);
      }

      // 2. معالجة المرادفات والأنماط
      const processedQuery = this.synonymProcessor.processQuery(userQuery);

      // 2.5. استخراج الكيانات
      const extractedEntities = this.extractEntities(userQuery, classificationResult.intent);

      // 3. استخدام النظام الذكي لاختيار الجدول والأعمدة
      const smartContext: SmartQueryContext = {
        intent: classificationResult.intent.name,
        entities: extractedEntities.map(e => e.value),
        queryType: 'single_table',
        comparisonItems: extractedEntities.filter(e => e.type === 'product' || e.type === 'customer').map(e => e.value)
      };

      const smartSelection = this.smartTableSelector.selectTableAndColumns(smartContext);
      const targetTable = smartSelection.primaryTable;
      const agentProfile = targetTable ? this.agentProfiles.get(targetTable) : undefined;

      // 3. إنشاء البرومبت المحسن
      const generatedPrompt = this.promptTemplateManager.generatePrompt(
        classificationResult,
        processedQuery,
        tableInfo,
        this.databaseSchema || undefined,
        agentProfile
      );

      // 4. توليد استعلام SQL باستخدام النظام الذكي أو التقليدي
      let sqlResult: SQLQueryResult;

      if (smartSelection.queryTemplate) {
        // استخدام القالب الذكي المحسن
        sqlResult = {
          query: smartSelection.queryTemplate,
          explanation: `تم استخدام النظام الذكي لاختيار الجدول ${smartSelection.primaryTable} والأعمدة المناسبة للاستعلام عن ${classificationResult.intent.name}`,
          confidence: 0.95,
          estimated_rows: 100
        };

        if (this.config.debugMode) {
          console.log('🧠 استخدام النظام الذكي:', smartSelection);
        }
      } else {
        // العودة للنظام التقليدي
        sqlResult = await this.generateSQL(generatedPrompt);
      }

      // 5. التحقق من القواعد والقيود
      const queryContext: QueryContext = {
        query: sqlResult.query,
        intent: classificationResult.intent.id,
        estimatedRows: sqlResult.estimated_rows || 100,
        tables: this.extractTables(sqlResult.query),
        columns: this.extractColumns(sqlResult.query)
      };

      const validationResult = this.rulesEngine.validateQuery(
        sqlResult.query,
        userContext,
        queryContext
      );

      if (!validationResult.isValid) {
        return this.createErrorResponse(
          validationResult.errors.join(', '),
          startTime,
          validationResult.warnings
        );
      }

      // 6. تنفيذ الاستعلام على قاعدة البيانات الحقيقية
      let results: any[] = [];
      try {
        results = await this.executeQuery(validationResult.sanitizedQuery || sqlResult.query);
      } catch (executionError) {
        console.error('❌ خطأ في تنفيذ الاستعلام:', executionError);
        return this.createErrorResponse(
          executionError instanceof Error ? executionError.message : 'فشل في تنفيذ الاستعلام',
          startTime
        );
      }

      // 7. تسجيل الاستعلام
      this.rulesEngine.logQuery(
        sqlResult.query,
        userContext,
        results,
        Date.now() - startTime
      );

      // التحقق من وجود نتائج فعلية
      if (!results || results.length === 0) {
        return {
          success: false,
          query: validationResult.sanitizedQuery || sqlResult.query,
          explanation: sqlResult.explanation,
          confidence: sqlResult.confidence,
          results: [],
          error: 'لم يتم العثور على أي نتائج للاستعلام المحدد',
          warnings: validationResult.warnings,
          metadata: {
            intent: classificationResult.intent.name,
            processingTimeMs: Date.now() - startTime,
            rulesApplied: validationResult.appliedRules,
            templateUsed: generatedPrompt.templateUsed,
            estimatedRows: 0
          }
        };
      }

      return {
        success: true,
        query: validationResult.sanitizedQuery || sqlResult.query,
        explanation: sqlResult.explanation,
        confidence: sqlResult.confidence,
        results: results,
        warnings: validationResult.warnings,
        metadata: {
          intent: classificationResult.intent.name,
          processingTimeMs: Date.now() - startTime,
          rulesApplied: validationResult.appliedRules,
          templateUsed: generatedPrompt.templateUsed,
          estimatedRows: results.length
        }
      };

    } catch (error) {
      console.error('خطأ في معالجة الاستعلام:', error);
      return this.createErrorResponse(
        'حدث خطأ أثناء معالجة استعلامك',
        startTime
      );
    }
  }

  /**
   * استخراج الكيانات من الاستعلام
   */
  private extractEntities(query: string, intent: any): Array<{type: string, value: string}> {
    const entities: Array<{type: string, value: string}> = [];
    const lowerQuery = query.toLowerCase();

    // للاستعلامات العامة مثل "أكثر منتج مبيعاً"، لا نحتاج كيانات محددة
    if (lowerQuery.includes('أكثر') || lowerQuery.includes('اكثر')) {
      if (lowerQuery.includes('منتج')) {
        entities.push({type: 'query_type', value: 'top_products'});
      } else if (lowerQuery.includes('عميل')) {
        entities.push({type: 'query_type', value: 'top_customers'});
      } else if (lowerQuery.includes('فرع')) {
        entities.push({type: 'query_type', value: 'top_branches'});
      }
    }

    // استخراج أسماء المنتجات المحددة
    const productPatterns = [
      /منتج\s+([^\s]+)/g,
      /صنف\s+([^\s]+)/g,
      /سلعة\s+([^\s]+)/g
    ];

    productPatterns.forEach(pattern => {
      const matches = lowerQuery.matchAll(pattern);
      for (const match of matches) {
        entities.push({type: 'product', value: match[1]});
      }
    });

    // استخراج أسماء العملاء المحتملة
    const customerPatterns = [
      /عميل\s+([^\s]+)/g,
      /زبون\s+([^\s]+)/g,
      /عميل\s+([^\s]+\s+[^\s]+)/g, // اسم مركب
      /([أ-ي]+\s+[أ-ي]+)/g // أسماء عربية
    ];

    customerPatterns.forEach(pattern => {
      const matches = lowerQuery.matchAll(pattern);
      for (const match of matches) {
        const name = match[1] || match[0];
        if (name && name.length > 2 && !['منتج', 'صنف', 'فرع', 'مبيعات'].includes(name)) {
          entities.push({type: 'customer', value: name});
        }
      }
    });

    // استخراج أسماء الفروع
    const branchPatterns = [
      /فرع\s+([^\s]+)/g,
      /(الرياض|جدة|الدمام|مكة|المدينة)/g
    ];

    branchPatterns.forEach(pattern => {
      const matches = lowerQuery.matchAll(pattern);
      for (const match of matches) {
        entities.push({type: 'branch', value: match[1] || match[0]});
      }
    });

    return entities;
  }

  /**
   * تحديد الجدول المناسب بناءً على النية
   */
  private determineTargetTable(classificationResult: ClassificationResult): string | null {
    // معظم النوايا تستخدم جدول العناصر الرئيسي
    const mainTables = ['tbltemp_ItemsMain', 'ItemsMain', 'items_main'];

    if (this.databaseSchema) {
      for (const tableName of mainTables) {
        if (this.databaseSchema.tables.some(t => t.name === tableName)) {
          return tableName;
        }
      }

      // إذا لم نجد الجدول الرئيسي، نأخذ أول جدول متاح
      if (this.databaseSchema.tables.length > 0) {
        return this.databaseSchema.tables[0].name;
      }
    }

    return null;
  }

  /**
   * تصنيف نية المستخدم
   */
  private async classifyIntent(userQuery: string): Promise<ClassificationResult | null> {
    const cacheKey = `intent_${userQuery}`;
    
    if (this.config.enableCaching && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const result = await this.intentClassifier.classifyIntent(userQuery);
    
    if (this.config.enableCaching && result) {
      this.cache.set(cacheKey, result);
    }

    return result;
  }

  /**
   * توليد استعلام SQL باستخدام OpenRouter
   */
  private async generateSQL(prompt: GeneratedPrompt): Promise<SQLQueryResult> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        if (this.config.debugMode) {
          console.log(`🔄 محاولة ${attempt} لتوليد SQL`);
        }

        const response = await this.openRouterClient.generateSQLQuery(
          prompt.userPrompt,
          [], // سيتم تمرير معلومات الجداول من tableInfo
          'mssql'
        );

        return {
          query: response.query,
          explanation: response.explanation,
          confidence: response.confidence,
          template_used: prompt.templateUsed,
          parameters_applied: prompt.parameters
        };

      } catch (error) {
        lastError = error as Error;
        if (this.config.debugMode) {
          console.log(`❌ فشلت المحاولة ${attempt}:`, error);
        }
        
        if (attempt < this.config.maxRetries) {
          await this.delay(1000 * attempt); // تأخير متزايد
        }
      }
    }

    throw lastError || new Error('فشل في توليد استعلام SQL');
  }

  /**
   * تنفيذ الاستعلام على قاعدة البيانات الحقيقية
   */
  private async executeQuery(query: string): Promise<any[]> {
    if (this.config.debugMode) {
      console.log('🗃️ تنفيذ الاستعلام:', query);
    }

    try {
      // تنظيف الاستعلام لـ SQL Server
      let cleanQuery = this.cleanSQLServerQuery(query);

      if (this.config.debugMode) {
        console.log('🧹 الاستعلام بعد التنظيف:', cleanQuery);
      }

      // استخدام SchemaManager لتنفيذ الاستعلام على قاعدة البيانات الحقيقية
      const schemaManager = SchemaManager.getInstance();
      const result = await schemaManager.executeQuery(cleanQuery);

      if (this.config.debugMode) {
        console.log(`✅ تم تنفيذ الاستعلام بنجاح: ${result.rowCount} صف`);
        console.log('📊 البيانات المُرجعة:', result.data);
      }

      return result.data || [];

    } catch (error) {
      if (this.config.debugMode) {
        console.error('❌ خطأ في تنفيذ الاستعلام:', error);
      }

      // في حالة الخطأ، رفع استثناء بدلاً من إرجاع مصفوفة خطأ
      throw error;
    }
  }

  /**
   * تنظيف الاستعلام لـ SQL Server
   */
  private cleanSQLServerQuery(query: string): string {
    let cleanQuery = query;

    // إزالة backticks واستبدالها بـ square brackets
    cleanQuery = cleanQuery.replace(/`([^`]+)`/g, '[$1]');

    // إزالة LIMIT إذا كان موجود مع TOP
    if (cleanQuery.toUpperCase().includes('TOP ') && cleanQuery.toUpperCase().includes('LIMIT')) {
      cleanQuery = cleanQuery.replace(/\s*LIMIT\s+\d+\s*/gi, '');
    }

    // إزالة WHERE clause المضافة خطأ في النهاية
    cleanQuery = cleanQuery.replace(/;\s*WHERE\s+BranchName\s*=\s*'[^']*'\s*$/gi, '');

    return cleanQuery.trim();
  }

  /**
   * استخراج أسماء الجداول من الاستعلام
   */
  private extractTables(query: string): string[] {
    const tables: string[] = [];
    const fromRegex = /FROM\s+(\w+)/gi;
    const joinRegex = /JOIN\s+(\w+)/gi;
    
    let match;
    while ((match = fromRegex.exec(query)) !== null) {
      tables.push(match[1]);
    }
    
    while ((match = joinRegex.exec(query)) !== null) {
      tables.push(match[1]);
    }
    
    return [...new Set(tables)]; // إزالة التكرارات
  }

  /**
   * استخراج أسماء الأعمدة من الاستعلام
   */
  private extractColumns(query: string): string[] {
    const columns: string[] = [];
    
    // استخراج الأعمدة من SELECT
    const selectMatch = query.match(/SELECT\s+(.*?)\s+FROM/i);
    if (selectMatch) {
      const selectClause = selectMatch[1];
      if (selectClause !== '*') {
        const columnMatches = selectClause.split(',');
        for (const col of columnMatches) {
          const cleanCol = col.trim().replace(/\s+as\s+\w+/i, '').trim();
          if (cleanCol && !cleanCol.includes('(')) { // تجاهل الدوال
            columns.push(cleanCol);
          }
        }
      }
    }
    
    // استخراج الأعمدة من WHERE
    const whereMatches = query.match(/WHERE\s+(.+?)(?:\s+GROUP\s+BY|\s+ORDER\s+BY|\s+LIMIT|$)/i);
    if (whereMatches) {
      const whereClause = whereMatches[1];
      const columnRegex = /(\w+)\s*[=<>!]/g;
      let match;
      while ((match = columnRegex.exec(whereClause)) !== null) {
        columns.push(match[1]);
      }
    }
    
    return [...new Set(columns)]; // إزالة التكرارات
  }

  /**
   * إنشاء استجابة خطأ
   */
  private createErrorResponse(
    error: string,
    startTime: number,
    warnings: string[] = []
  ): AgentResponse {
    return {
      success: false,
      error: error,
      warnings: warnings,
      metadata: {
        intent: 'unknown',
        processingTimeMs: Date.now() - startTime,
        rulesApplied: []
      }
    };
  }

  /**
   * تأخير لفترة محددة
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * الحصول على إحصائيات الأداء
   */
  getPerformanceStats(): any {
    return {
      cacheSize: this.cache.size,
      cacheHitRate: 0, // سيتم حسابها في التطبيق الحقيقي
      averageProcessingTime: 0, // سيتم حسابها في التطبيق الحقيقي
      totalQueries: 0 // سيتم حسابها في التطبيق الحقيقي
    };
  }

  /**
   * مسح الذاكرة المؤقتة
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * تحديث إعدادات الوكيل
   */
  updateConfig(newConfig: Partial<AgentConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * الحصول على النوايا المتاحة
   */
  getAvailableIntents(): any[] {
    return this.intentClassifier.getAvailableIntents();
  }

  /**
   * الحصول على القوالب المتاحة
   */
  getAvailableTemplates(): any {
    return this.promptTemplateManager.getAvailableTemplates();
  }

  /**
   * الحصول على Database Schema
   */
  getDatabaseSchema(): DatabaseSchema | null {
    return this.databaseSchema;
  }

  /**
   * الحصول على Agent Profile لجدول معين
   */
  getAgentProfile(tableName: string): AgentProfile | undefined {
    return this.agentProfiles.get(tableName);
  }

  /**
   * الحصول على جميع Agent Profiles
   */
  getAllAgentProfiles(): Map<string, AgentProfile> {
    return this.agentProfiles;
  }

  /**
   * إعادة توليد Schema وProfiles
   */
  async regenerateSchemaAndProfiles(): Promise<void> {
    if (this.config.debugMode) {
      console.log('🔄 إعادة توليد Schema وAgent Profiles...');
    }

    // مسح البيانات الحالية
    this.databaseSchema = null;
    this.agentProfiles.clear();

    // إعادة التهيئة
    await this.initializeSchemaAndProfiles();
  }

  /**
   * حفظ Schema وProfiles يدوياً
   */
  async saveSchemaAndProfiles(): Promise<void> {
    try {
      if (this.databaseSchema) {
        await this.schemaGenerator.saveSchemaToFile(this.databaseSchema);
      }

      for (const [tableName, profile] of this.agentProfiles) {
        await this.agentProfileGenerator.saveProfileToFile(profile, `agent-profile-${tableName}.json`);
      }

      if (this.config.debugMode) {
        console.log('✅ تم حفظ Schema وProfiles بنجاح');
      }
    } catch (error) {
      console.error('❌ خطأ في حفظ Schema وProfiles:', error);
    }
  }

  /**
   * تحويل Schema الخام إلى Schema محسن مع الأوصاف
   */
  private async convertRawSchemaToEnhanced(rawSchema: any): Promise<DatabaseSchema> {
    const enhancedTables = rawSchema.tables.map((table: any) => {
      const enhancedColumns = table.columns.map((col: any) => {
        const category = this.schemaGenerator.categorizeColumn(col.name, col.type);
        const isImportant = this.schemaGenerator.isColumnImportant(col.name, col.type, category);
        const description = this.schemaGenerator.generateColumnDescription(col.name, col.type, category);

        return {
          name: col.name,
          type: col.type,
          description,
          isImportant,
          category
        };
      });

      return {
        name: table.name,
        description: this.schemaGenerator.generateTableDescription(table.name, enhancedColumns),
        columns: enhancedColumns,
        primaryKey: table.primaryKeys?.[0] || null
      };
    });

    return {
      tables: enhancedTables,
      version: '1.0.0',
      generatedAt: new Date().toISOString(),
      metadata: {
        totalTables: enhancedTables.length,
        totalColumns: enhancedTables.reduce((sum: number, table: any) => sum + table.columns.length, 0),
        importantColumns: enhancedTables.reduce((sum: number, table: any) =>
          sum + table.columns.filter((col: any) => col.isImportant).length, 0)
      }
    };
  }
}
